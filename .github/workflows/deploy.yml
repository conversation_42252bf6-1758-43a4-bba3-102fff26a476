name: FindU App CI/CD (Test on PR, Deploy on Main)

on:
  # 在Pull Request时运行测试
  pull_request:
    branches:
      - main
  # 在推送到main分支时运行测试和部署
  push:
    branches:
      - main

jobs:
  test_frontend:
    name: Frontend Tests (Vitest)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        working-directory: findu-frontend
        run: npm install

      - name: Run unit tests
        working-directory: findu-frontend
        run: |
          if npm run --silent test -- --version >/dev/null 2>&1; then
            npm run test
          else
            echo "No test script found. Skipping.";
          fi

  test_backend:
    name: Backend Tests (pytest)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install backend dependencies
        working-directory: findu-backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest

      - name: Run backend tests
        working-directory: findu-backend
        run: pytest -q

  build_and_deploy:
    name: Build frontend, then deploy both services to official-site server
    runs-on: ubuntu-latest
    needs: [test_frontend, test_backend]
    # 只在推送到main分支时运行部署
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install frontend dependencies
        working-directory: findu-frontend
        run: npm install

      - name: Build and Export static site
        working-directory: findu-frontend
        env:
          # 设置生产环境的API URL，指向部署后的后端服务
          NEXT_PUBLIC_API_URL: /api
          NEXT_PUBLIC_APP_NAME: FindU AI需求生成器
          NEXT_PUBLIC_DEFAULT_LOCALE: en
        run: |
          npm run build
          # Export to `out/` for nginx static serving
          if npm run --silent export >/dev/null 2>&1; then
            npm run export
          else
            npx next export
          fi

      - name: Upload docker-compose definition
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "deploy/docker-compose.yml"
          target: "~/official-site/apps/findu"
          strip_components: 1
          overwrite: true

      - name: Upload frontend static 'out' directory
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "findu-frontend/out/*"
          target: "~/official-site/apps/findu/html"
          strip_components: 2
          overwrite: true

      - name: Upload backend source code
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "findu-backend/**"
          target: "~/official-site/apps/findu/findu-backend"
          strip_components: 1
          overwrite: true

      - name: Upload nginx configuration
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "deploy/nginx-findu.conf"
          target: "~/official-site/apps/findu"
          strip_components: 1
          overwrite: true

      - name: Upload environment template
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "deploy/.env.example"
          target: "~/official-site/apps/findu"
          strip_components: 1
          overwrite: true

      - name: Upload setup scripts
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "deploy/setup-nginx.sh"
          target: "~/official-site/apps/findu"
          strip_components: 1
          overwrite: true

      - name: Launch/Update services with docker compose
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -e
            mkdir -p ~/official-site/apps/findu
            cd ~/official-site/apps/findu
            # Ensure directories exist
            mkdir -p html
            mkdir -p findu-backend
            # Set execute permissions for scripts
            chmod +x setup-nginx.sh
            # Create .env file if it doesn't exist
            if [ ! -f .env ]; then
              echo "创建默认 .env 文件..."
              cp .env.example .env
              echo "⚠️  请编辑 .env 文件并设置正确的配置值"
            fi
            # Stop existing services if running
            if [ -f docker-compose.yml ]; then
              if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
                docker compose down || true
              else
                docker-compose down || true
              fi
            fi
            # Bring up services (prefer Docker Compose V2)
            if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
              docker compose up -d --build
            else
              docker-compose up -d --build
            fi
            echo "✅ FindU 应用部署完成！"
            echo "📍 前端访问地址: http://your-domain/findu/"
            echo "📍 API访问地址: http://your-domain/findu/api/"
            echo "⚠️  请确保主站 Nginx 已包含 FindU 配置"

