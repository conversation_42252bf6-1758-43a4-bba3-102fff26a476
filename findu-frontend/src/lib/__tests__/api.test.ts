import { generateCases, generateDemand } from '../api';

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set default environment variable
    process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8000';
  });

  describe('generateCases', () => {
    it('makes correct API call for case generation', async () => {
      const mockResponse = {
        cases: [
          {
            id: 0,
            title: 'E-commerce Website',
            description: 'Online shopping platform',
            details: ['User registration', 'Product catalog'],
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await generateCases({
        prompt: 'I want to build an e-commerce website',
        locale: 'en',
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/generate-cases',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: 'I want to build an e-commerce website',
            locale: 'en',
          }),
        },
      );

      expect(result).toEqual(mockResponse);
    });

    it('handles API errors correctly', async () => {
      const errorResponse = {
        detail: {
          error: 'ai_api_error',
          message: 'AI service unavailable',
          provider: 'openai',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        json: async () => errorResponse,
      });

      await expect(generateCases({
        prompt: 'test prompt',
        locale: 'en',
      })).rejects.toThrow('AI service unavailable');
    });

    it('handles network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(generateCases({
        prompt: 'test prompt',
        locale: 'en',
      })).rejects.toThrow('Network error');
    });

    it('handles non-JSON error responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => {
          throw new Error('Invalid JSON');
        },
        text: async () => 'Internal Server Error',
      });

      await expect(generateCases({
        prompt: 'test prompt',
        locale: 'en',
      })).rejects.toThrow('生成案例失败 (HTTP 500)');
    });

    it('uses default locale when not provided', async () => {
      const mockResponse = { cases: [] };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await generateCases({ prompt: 'test prompt', locale: 'en' });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            prompt: 'test prompt',
            locale: 'en',
          }),
        }),
      );
    });
  });

  describe('generateDemand', () => {
    it('makes correct API call for document generation', async () => {
      const mockResponse = {
        document_url: '/static/demands/test-document.pdf',
        content: 'Mock document content',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const requestData = {
        case_id: 0,
        case_title: 'E-commerce Website',
        case_description: 'Online shopping platform',
        case_details: ['User registration', 'Product catalog'],
        locale: 'en',
        format: 'pdf' as const,
      };

      const result = await generateDemand(requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/generate-demand',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        },
      );

      expect(result).toEqual(mockResponse);
    });

    it('handles document generation errors', async () => {
      const errorResponse = {
        detail: {
          error: 'ai_response_error',
          message: 'Document generation failed',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => errorResponse,
      });

      await expect(generateDemand({
        case_id: 0,
        case_title: 'Test',
        case_description: 'Test description',
        case_details: ['Detail 1'],
        locale: 'en',
        format: 'pdf',
      })).rejects.toThrow('Document generation failed');
    });

    it('supports different document formats', async () => {
      const mockResponse = {
        document_url: '/static/demands/test-document.docx',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await generateDemand({
        case_id: 0,
        case_title: 'Test',
        case_description: 'Test description',
        case_details: ['Detail 1'],
        locale: 'en',
        format: 'docx',
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            case_id: 0,
            case_title: 'Test',
            case_description: 'Test description',
            case_details: ['Detail 1'],
            locale: 'en',
            format: 'docx',
          }),
        }),
      );
    });
  });

  describe('API URL configuration', () => {
    it('uses environment variable for API URL', async () => {
      process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com';
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ cases: [] }),
      });

      await generateCases({ prompt: 'test' });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.example.com/generate-cases',
        expect.any(Object),
      );
    });

    it('falls back to default URL when environment variable is not set', async () => {
      delete process.env.NEXT_PUBLIC_API_URL;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ cases: [] }),
      });

      await generateCases({ prompt: 'test', locale: 'en' });

      expect(mockFetch).toHaveBeenCalledWith(
        'undefined/generate-cases',
        expect.any(Object),
      );
    });
  });
});
