import { describe, it, expect } from '@jest/globals';
import { cn, formatFileSize, truncateText } from './utils';

describe('utils', () => {
  it('cn merges classes correctly', () => {
    expect(cn('a', { b: true }, ['c'])).toContain('a');
  });

  it('formatFileSize formats bytes', () => {
    expect(formatFileSize(0)).toBe('0 Bytes');
    expect(formatFileSize(1024)).toContain('KB');
  });

  it('truncateText truncates long text', () => {
    expect(truncateText('hello', 10)).toBe('hello');
    expect(truncateText('helloworld', 5)).toBe('hello...');
  });
});

