# FindU 项目 CI/CD 配置修改总结

本文档总结了为 FindU 项目配置 CI/CD 流程所做的所有修改，使其能够在 Pull Request 时自动测试，并在合并到 main 分支后自动部署到服务器的 `~/official-site/apps/findu/` 路径。

## 🔄 修改的文件

### 1. `.github/workflows/deploy.yml`
**主要修改**:
- 添加了 Pull Request 触发器，实现 PR 时自动测试
- 修改部署路径为 `~/official-site/apps/findu/`
- 更新 secrets 名称为与原配置兼容的格式
- 添加前端构建时的环境变量配置
- 优化部署脚本，包含环境检查和服务管理

**新功能**:
- PR 时运行测试但不部署
- 推送到 main 分支时运行测试并部署
- 自动创建 .env 文件
- 上传 Nginx 配置和设置脚本

### 2. `deploy/docker-compose.yml`
**主要修改**:
- 更新服务名称为 `findu-frontend` 和 `findu-backend`
- 修改端口映射：前端 8001，后端 8002
- 添加网络配置确保服务间通信
- 增加环境变量支持，包括 AI 服务配置
- 优化 CORS 配置支持主域名访问

### 3. 新增文件

#### `deploy/nginx-findu.conf`
- Nginx 配置片段，用于反向代理 FindU 应用
- 支持前端静态文件、API 和静态资源的路由
- 包含缓存和性能优化配置

#### `deploy/.env.example`
- 生产环境配置模板
- 包含 JWT、AI 服务、存储等配置项
- 支持多种 AI 服务提供商

#### `deploy/setup-nginx.sh`
- 自动化 Nginx 配置脚本
- 提供多种配置方式选择
- 包含配置测试和重载功能

#### `deploy/README.md`
- 详细的部署指南
- 包含配置说明和故障排除
- 提供完整的环境变量配置示例

## 🚀 部署流程

### Pull Request 流程
1. 触发条件：向 main 分支提交 PR
2. 执行步骤：
   - 前端测试 (Vitest)
   - 后端测试 (pytest)
3. 结果：测试通过/失败，不进行部署

### 主分支部署流程
1. 触发条件：推送到 main 分支
2. 执行步骤：
   - 前端测试 (Vitest)
   - 后端测试 (pytest)
   - 构建前端静态文件
   - 上传文件到服务器
   - 启动/更新 Docker 服务
3. 结果：应用部署到生产环境

## 🔧 服务器配置

### 目录结构
```
~/official-site/
├── html/                    # 主站静态文件
├── docker-compose.yml       # 主站服务
└── apps/
    └── findu/              # FindU 应用
        ├── html/           # 前端静态文件
        ├── findu-backend/  # 后端源码
        ├── docker-compose.yml
        ├── nginx-findu.conf
        ├── .env
        └── setup-nginx.sh
```

### 端口分配
- 主站 Nginx: 8000
- FindU 前端: 8001
- FindU 后端: 8002

### 访问地址
- 前端: `http://域名/findu/`
- API: `http://域名/findu/api/`
- 静态文件: `http://域名/findu/static/`

## 📋 必需的 GitHub Secrets

在 GitHub 仓库设置中添加以下 secrets：

```
SERVER_HOST=服务器IP地址
SERVER_USER=ubuntu
SSH_PRIVATE_KEY=SSH私钥内容
```

## ⚙️ 部署后配置步骤

1. **配置环境变量**:
   ```bash
   cd ~/official-site/apps/findu
   nano .env
   ```

2. **配置 Nginx**:
   ```bash
   ./setup-nginx.sh
   ```

3. **重启服务**:
   ```bash
   docker compose restart
   sudo systemctl reload nginx
   ```

## 🔍 验证部署

1. 检查服务状态：
   ```bash
   cd ~/official-site/apps/findu
   docker compose ps
   ```

2. 查看服务日志：
   ```bash
   docker compose logs
   ```

3. 测试访问：
   - 前端：`http://your-domain/findu/`
   - API 健康检查：`http://your-domain/findu/api/health`

## 🛠️ 故障排除

### 常见问题
1. **服务无法启动**：检查 .env 文件配置
2. **前端无法访问**：检查 Nginx 配置
3. **API 调用失败**：检查 CORS 和网络配置
4. **AI 服务错误**：检查 AI API 密钥和配置

### 日志查看
```bash
# 查看所有服务日志
docker compose logs

# 查看特定服务日志
docker compose logs findu-backend
docker compose logs findu-frontend

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/error.log
```

## 📝 注意事项

1. **环境变量安全**：确保 .env 文件包含正确的 JWT 密钥和 AI API 密钥
2. **Nginx 配置**：必须将 FindU 配置集成到主站 Nginx 中
3. **端口冲突**：确保端口 8001 和 8002 未被其他服务占用
4. **权限设置**：确保 ubuntu 用户有权限访问部署目录
5. **备份配置**：建议在修改 Nginx 配置前进行备份

## 🎉 完成

配置完成后，FindU 应用将：
- 在每次 PR 时自动运行测试
- 在合并到 main 分支后自动部署
- 通过主域名的 `/findu/` 路径提供服务
- 与现有的官方网站和谐共存
