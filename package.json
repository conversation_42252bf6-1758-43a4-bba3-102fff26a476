{"name": "findu-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "e2e:headed": "playwright test --headed"}, "dependencies": {"clsx": "^2.1.1", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^2.5.4", "use-debounce": "^10.0.5", "react-pdf": "^10.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^2.0.0"}}