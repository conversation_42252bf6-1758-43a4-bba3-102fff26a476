"""
字体管理核心模块

该模块提供统一的字体管理功能，支持：
- 多语言字体配置和管理
- 字体文件的自动发现和注册
- 基于语言的字体选择和回退机制
- 可扩展的字体配置系统

设计原则：
- 解耦字体管理与文档生成逻辑
- 支持项目内置字体，减少系统依赖
- 提供灵活的配置和扩展机制
- 确保字体加载的稳定性和性能
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)

class FontWeight(Enum):
    """字体粗细枚举"""
    THIN = "thin"
    EXTRA_LIGHT = "extra_light"
    LIGHT = "light"
    REGULAR = "regular"
    MEDIUM = "medium"
    SEMI_BOLD = "semi_bold"
    BOLD = "bold"
    EXTRA_BOLD = "extra_bold"
    BLACK = "black"

class FontStyle(Enum):
    """字体样式枚举"""
    NORMAL = "normal"
    ITALIC = "italic"
    OBLIQUE = "oblique"

@dataclass
class FontInfo:
    """字体信息数据类"""
    name: str                           # 字体名称
    file_path: str                      # 字体文件路径
    weight: FontWeight = FontWeight.REGULAR  # 字体粗细
    style: FontStyle = FontStyle.NORMAL      # 字体样式
    language: str = "en"                     # 支持的语言
    is_variable: bool = False               # 是否为可变字体
    subfont_index: int = 0                  # TTC文件中的子字体索引
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据

@dataclass
class LanguageFontConfig:
    """语言字体配置"""
    language: str                       # 语言代码
    display_name: str                   # 显示名称
    primary_fonts: List[FontInfo]       # 主要字体列表
    fallback_fonts: List[FontInfo]      # 回退字体列表
    default_weight: FontWeight = FontWeight.REGULAR
    default_style: FontStyle = FontStyle.NORMAL

class FontManager:
    """字体管理器
    
    负责字体的发现、注册、配置和管理。
    支持多语言字体配置和动态字体加载。
    """
    
    def __init__(self, fonts_dir: Optional[str] = None, config_file: Optional[str] = None):
        """
        初始化字体管理器
        
        Args:
            fonts_dir: 字体文件目录路径
            config_file: 字体配置文件路径
        """
        self.fonts_dir = fonts_dir or self._get_default_fonts_dir()
        self.config_file = config_file or os.path.join(self.fonts_dir, "font_config.json")
        
        # 字体注册表
        self._registered_fonts: Dict[str, FontInfo] = {}
        self._language_configs: Dict[str, LanguageFontConfig] = {}
        
        # 初始化状态
        self._initialized = False
        
        # 加载配置和字体
        self._load_config()
        self._discover_fonts()
    
    def _get_default_fonts_dir(self) -> str:
        """获取默认字体目录"""
        current_dir = Path(__file__).parent.parent.parent
        return str(current_dir / "fonts")
    
    def _load_config(self) -> None:
        """加载字体配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self._parse_config(config_data)
                logger.info(f"已加载字体配置文件: {self.config_file}")
            else:
                logger.info("字体配置文件不存在，将使用默认配置")
                self._create_default_config()
        except Exception as e:
            logger.error(f"加载字体配置失败: {e}")
            self._create_default_config()
    
    def _parse_config(self, config_data: Dict[str, Any]) -> None:
        """解析配置数据"""
        languages = config_data.get("languages", {})
        
        for lang_code, lang_config in languages.items():
            try:
                # 解析主要字体
                primary_fonts = []
                for font_data in lang_config.get("primary_fonts", []):
                    font_info = self._create_font_info_from_config(font_data)
                    if font_info:
                        primary_fonts.append(font_info)
                
                # 解析回退字体
                fallback_fonts = []
                for font_data in lang_config.get("fallback_fonts", []):
                    font_info = self._create_font_info_from_config(font_data)
                    if font_info:
                        fallback_fonts.append(font_info)
                
                # 创建语言配置
                lang_font_config = LanguageFontConfig(
                    language=lang_code,
                    display_name=lang_config.get("display_name", lang_code),
                    primary_fonts=primary_fonts,
                    fallback_fonts=fallback_fonts,
                    default_weight=FontWeight(lang_config.get("default_weight", "regular")),
                    default_style=FontStyle(lang_config.get("default_style", "normal"))
                )
                
                self._language_configs[lang_code] = lang_font_config
                
            except Exception as e:
                logger.error(f"解析语言配置失败 {lang_code}: {e}")
    
    def _create_font_info_from_config(self, font_data: Dict[str, Any]) -> Optional[FontInfo]:
        """从配置数据创建字体信息"""
        try:
            file_path = font_data.get("file_path", "")
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.fonts_dir, file_path)
            
            if not os.path.exists(file_path):
                logger.warning(f"字体文件不存在: {file_path}")
                return None
            
            return FontInfo(
                name=font_data.get("name", ""),
                file_path=file_path,
                weight=FontWeight(font_data.get("weight", "regular")),
                style=FontStyle(font_data.get("style", "normal")),
                language=font_data.get("language", "en"),
                is_variable=font_data.get("is_variable", False),
                subfont_index=font_data.get("subfont_index", 0),
                metadata=font_data.get("metadata", {})
            )
        except Exception as e:
            logger.error(f"创建字体信息失败: {e}")
            return None
    
    def _create_default_config(self) -> None:
        """创建默认字体配置"""
        default_config = {
            "version": "1.0",
            "description": "FindU 字体管理配置文件",
            "languages": {
                "zh": {
                    "display_name": "中文",
                    "primary_fonts": [
                        {
                            "name": "NotoSansSC-Regular",
                            "file_path": "Noto_Sans_SC/static/NotoSansSC-Regular.ttf",
                            "weight": "regular",
                            "style": "normal",
                            "language": "zh"
                        },
                        {
                            "name": "NotoSansSC-Bold",
                            "file_path": "Noto_Sans_SC/static/NotoSansSC-Bold.ttf",
                            "weight": "bold",
                            "style": "normal",
                            "language": "zh"
                        }
                    ],
                    "fallback_fonts": [],
                    "default_weight": "regular",
                    "default_style": "normal"
                },
                "en": {
                    "display_name": "English",
                    "primary_fonts": [],
                    "fallback_fonts": [],
                    "default_weight": "regular",
                    "default_style": "normal"
                }
            }
        }
        
        self._parse_config(default_config)
        
        # 保存默认配置到文件
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            logger.info(f"已创建默认字体配置文件: {self.config_file}")
        except Exception as e:
            logger.error(f"保存默认配置失败: {e}")
    
    def _discover_fonts(self) -> None:
        """自动发现字体文件"""
        if not os.path.exists(self.fonts_dir):
            logger.warning(f"字体目录不存在: {self.fonts_dir}")
            return
        
        font_extensions = {'.ttf', '.otf', '.ttc', '.woff', '.woff2'}
        discovered_fonts = []
        
        for root, dirs, files in os.walk(self.fonts_dir):
            for file in files:
                if any(file.lower().endswith(ext) for ext in font_extensions):
                    font_path = os.path.join(root, file)
                    discovered_fonts.append(font_path)
        
        logger.info(f"发现 {len(discovered_fonts)} 个字体文件")
        
        # 注册发现的字体到对应语言配置
        for font_path in discovered_fonts:
            self._register_discovered_font(font_path)
    
    def _register_discovered_font(self, font_path: str) -> None:
        """注册发现的字体文件"""
        try:
            # 从路径推断字体信息
            font_name = os.path.splitext(os.path.basename(font_path))[0]
            
            # 简单的语言推断逻辑
            language = "en"  # 默认英文
            if any(keyword in font_path.lower() for keyword in ['noto', 'sc', 'chinese', 'zh', 'cjk']):
                language = "zh"
            
            # 推断字体粗细
            weight = FontWeight.REGULAR
            if 'bold' in font_name.lower():
                weight = FontWeight.BOLD
            elif 'light' in font_name.lower():
                weight = FontWeight.LIGHT
            elif 'thin' in font_name.lower():
                weight = FontWeight.THIN
            
            font_info = FontInfo(
                name=font_name,
                file_path=font_path,
                weight=weight,
                style=FontStyle.NORMAL,
                language=language
            )
            
            self._registered_fonts[font_name] = font_info

        except Exception as e:
            logger.error(f"注册字体失败 {font_path}: {e}")

    def get_font_for_language(self, language: str, weight: Optional[FontWeight] = None,
                             style: Optional[FontStyle] = None) -> Optional[FontInfo]:
        """
        获取指定语言的字体

        Args:
            language: 语言代码
            weight: 字体粗细
            style: 字体样式

        Returns:
            FontInfo: 匹配的字体信息，如果没有找到则返回None
        """
        # 获取语言配置
        lang_config = self._language_configs.get(language)
        if not lang_config:
            # 尝试回退到英文
            lang_config = self._language_configs.get("en")
            if not lang_config:
                logger.warning(f"未找到语言配置: {language}")
                return None

        # 使用默认值
        target_weight = weight or lang_config.default_weight
        target_style = style or lang_config.default_style

        # 首先在主要字体中查找
        font = self._find_matching_font(lang_config.primary_fonts, target_weight, target_style)
        if font:
            return font

        # 在回退字体中查找
        font = self._find_matching_font(lang_config.fallback_fonts, target_weight, target_style)
        if font:
            return font

        # 如果仍未找到，返回第一个可用字体
        if lang_config.primary_fonts:
            return lang_config.primary_fonts[0]
        if lang_config.fallback_fonts:
            return lang_config.fallback_fonts[0]

        logger.warning(f"未找到适合的字体: {language}")
        return None

    def _find_matching_font(self, fonts: List[FontInfo], weight: FontWeight,
                           style: FontStyle) -> Optional[FontInfo]:
        """在字体列表中查找匹配的字体"""
        # 精确匹配
        for font in fonts:
            if font.weight == weight and font.style == style:
                return font

        # 样式匹配，粗细不限
        for font in fonts:
            if font.style == style:
                return font

        # 粗细匹配，样式不限
        for font in fonts:
            if font.weight == weight:
                return font

        return None

    def get_font_path(self, language: str, weight: Optional[FontWeight] = None,
                     style: Optional[FontStyle] = None) -> Optional[str]:
        """
        获取指定语言字体的文件路径

        Args:
            language: 语言代码
            weight: 字体粗细
            style: 字体样式

        Returns:
            str: 字体文件路径，如果没有找到则返回None
        """
        font = self.get_font_for_language(language, weight, style)
        return font.file_path if font else None

    def get_reportlab_font_name(self, language: str, weight: Optional[FontWeight] = None,
                               style: Optional[FontStyle] = None) -> Optional[str]:
        """
        获取ReportLab使用的字体名称

        Args:
            language: 语言代码
            weight: 字体粗细
            style: 字体样式

        Returns:
            str: ReportLab字体名称，如果没有找到则返回None
        """
        font = self.get_font_for_language(language, weight, style)
        if not font:
            return None

        # 生成ReportLab字体名称
        base_name = f"FindU-{font.language.upper()}-{font.name}"
        if weight == FontWeight.BOLD or font.weight == FontWeight.BOLD:
            base_name += "-Bold"
        if style == FontStyle.ITALIC or font.style == FontStyle.ITALIC:
            base_name += "-Italic"

        return base_name

    def register_fonts_to_reportlab(self, language: Optional[str] = None) -> Dict[str, bool]:
        """
        将字体注册到ReportLab

        Args:
            language: 指定语言，如果为None则注册所有语言的字体

        Returns:
            Dict[str, bool]: 注册结果，键为字体名称，值为是否成功
        """
        try:
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
        except ImportError:
            logger.error("ReportLab未安装，无法注册字体")
            return {}

        results = {}
        languages_to_process = [language] if language else self._language_configs.keys()

        for lang in languages_to_process:
            lang_config = self._language_configs.get(lang)
            if not lang_config:
                continue

            # 注册主要字体
            for font in lang_config.primary_fonts:
                font_name = self.get_reportlab_font_name(lang, font.weight, font.style)
                if font_name:
                    try:
                        if font_name not in pdfmetrics.getRegisteredFontNames():
                            if font.file_path.lower().endswith('.ttc'):
                                pdfmetrics.registerFont(TTFont(font_name, font.file_path,
                                                             subfontIndex=font.subfont_index))
                            else:
                                pdfmetrics.registerFont(TTFont(font_name, font.file_path))

                            results[font_name] = True
                            logger.info(f"成功注册字体到ReportLab: {font_name}")
                        else:
                            results[font_name] = True
                            logger.debug(f"字体已注册: {font_name}")
                    except Exception as e:
                        results[font_name] = False
                        logger.error(f"注册字体失败 {font_name}: {e}")

            # 注册回退字体
            for font in lang_config.fallback_fonts:
                font_name = self.get_reportlab_font_name(lang, font.weight, font.style)
                if font_name:
                    try:
                        if font_name not in pdfmetrics.getRegisteredFontNames():
                            if font.file_path.lower().endswith('.ttc'):
                                pdfmetrics.registerFont(TTFont(font_name, font.file_path,
                                                             subfontIndex=font.subfont_index))
                            else:
                                pdfmetrics.registerFont(TTFont(font_name, font.file_path))

                            results[font_name] = True
                            logger.info(f"成功注册回退字体到ReportLab: {font_name}")
                        else:
                            results[font_name] = True
                    except Exception as e:
                        results[font_name] = False
                        logger.error(f"注册回退字体失败 {font_name}: {e}")

        return results

    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        return list(self._language_configs.keys())

    def get_language_display_name(self, language: str) -> str:
        """获取语言的显示名称"""
        lang_config = self._language_configs.get(language)
        return lang_config.display_name if lang_config else language

    def is_language_supported(self, language: str) -> bool:
        """检查是否支持指定语言"""
        return language in self._language_configs

    def get_font_info(self, font_name: str) -> Optional[FontInfo]:
        """根据字体名称获取字体信息"""
        return self._registered_fonts.get(font_name)

    def list_fonts_for_language(self, language: str) -> List[FontInfo]:
        """列出指定语言的所有字体"""
        lang_config = self._language_configs.get(language)
        if not lang_config:
            return []

        return lang_config.primary_fonts + lang_config.fallback_fonts

    def reload_config(self) -> bool:
        """重新加载字体配置"""
        try:
            self._language_configs.clear()
            self._registered_fonts.clear()
            self._load_config()
            self._discover_fonts()
            logger.info("字体配置重新加载成功")
            return True
        except Exception as e:
            logger.error(f"重新加载字体配置失败: {e}")
            return False


# 全局字体管理器实例
_font_manager: Optional[FontManager] = None

def get_font_manager() -> FontManager:
    """获取全局字体管理器实例"""
    global _font_manager
    if _font_manager is None:
        _font_manager = FontManager()
    return _font_manager

def initialize_font_manager(fonts_dir: Optional[str] = None,
                          config_file: Optional[str] = None) -> FontManager:
    """初始化全局字体管理器"""
    global _font_manager
    _font_manager = FontManager(fonts_dir, config_file)
    return _font_manager
