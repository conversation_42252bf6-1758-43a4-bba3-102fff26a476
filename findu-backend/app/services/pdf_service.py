"""
PDF文档生成服务模块 (重构版)

该模块负责将文本内容转换为PDF文档，使用统一的字体管理系统。
主要功能包括：
- 将 Markdown 格式的文本转换为 PDF
- 支持多语言字体配置和管理
- 基于语言的字体自动选择
- 优化的样式和布局设计

技术实现：
- 使用 reportlab 库进行 PDF 转换
- 集成 FontManager 进行字体管理
- 支持项目内置字体，减少系统依赖
- 内存中生成文档，直接返回字节流

该模块为 FindU 系统提供高效的PDF文档输出功能，支持多语言和可扩展的字体配置。
"""

import os
import logging
from typing import Optional

# 导入字体管理系统
from app.core.font_manager import get_font_manager, FontWeight, FontStyle

# 配置日志
logger = logging.getLogger(__name__)

# 尝试导入 reportlab，如果失败则使用降级方案
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib.enums import TA_LEFT, TA_CENTER
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
    logger.info("reportlab 可用")
except ImportError:
    REPORTLAB_AVAILABLE = False
    logger.warning("reportlab 不可用，将使用文本文件降级方案")

def _register_fonts_for_language(language: str = "zh") -> bool:
    """
    使用字体管理器为指定语言注册字体到 ReportLab

    Args:
        language: 语言代码，默认为中文

    Returns:
        bool: 字体注册是否成功
    """
    try:
        font_manager = get_font_manager()

        # 注册指定语言的字体
        registration_results = font_manager.register_fonts_to_reportlab(language)

        if registration_results:
            successful_registrations = sum(1 for success in registration_results.values() if success)
            total_registrations = len(registration_results)

            logger.info(f"字体注册完成: {successful_registrations}/{total_registrations} 成功")

            # 只要有一个字体注册成功就认为成功
            return successful_registrations > 0
        else:
            logger.warning(f"未找到 {language} 语言的字体配置")
            return False

    except Exception as e:
        logger.error(f"字体注册过程出错: {e}")
        return False

def _get_font_names_for_language(language: str = "zh") -> tuple[str, str]:
    """
    获取指定语言的字体名称

    Args:
        language: 语言代码

    Returns:
        tuple: (常规字体名称, 粗体字体名称)
    """
    try:
        font_manager = get_font_manager()

        # 获取常规字体
        regular_font_name = font_manager.get_reportlab_font_name(language, FontWeight.REGULAR)
        bold_font_name = font_manager.get_reportlab_font_name(language, FontWeight.BOLD)

        # 如果没有找到粗体字体，使用常规字体
        if not bold_font_name:
            bold_font_name = regular_font_name

        # 如果都没有找到，使用默认字体
        if not regular_font_name:
            if language == "zh":
                regular_font_name = "Times-Roman"  # 回退到系统字体
                bold_font_name = "Times-Bold"
            else:
                regular_font_name = "Helvetica"
                bold_font_name = "Helvetica-Bold"

        return regular_font_name, bold_font_name

    except Exception as e:
        logger.error(f"获取字体名称失败: {e}")
        # 返回默认字体
        if language == "zh":
            return "Times-Roman", "Times-Bold"
        else:
            return "Helvetica", "Helvetica-Bold"

def _generate_pdf_with_reportlab(content: str, output_file: str, language: str = "zh") -> bool:
    """
    使用 reportlab 生成 PDF 文件 (重构版)

    Args:
        content (str): 文档内容
        output_file (str): 输出文件路径
        language (str): 语言代码，用于选择合适的字体

    Returns:
        bool: 生成成功返回 True，否则返回 False
    """
    try:
        # 使用字体管理器注册字体
        font_available = _register_fonts_for_language(language)

        if not font_available:
            logger.warning(f"{language} 语言字体注册失败，将使用默认字体")

        # 获取字体名称
        font_name, font_name_bold = _get_font_names_for_language(language)

        # 创建 PDF 文档，设置更详细的参数
        doc = SimpleDocTemplate(
            output_file,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )

        # 获取样式表
        styles = getSampleStyleSheet()

        # 创建自定义样式，使用字体管理器提供的字体
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=font_name_bold,
            fontSize=18,
            spaceAfter=30,
            spaceBefore=20,
            alignment=TA_CENTER,
            textColor='black'
        )

        heading1_style = ParagraphStyle(
            'CustomHeading1',
            parent=styles['Heading1'],
            fontName=font_name_bold,
            fontSize=16,
            spaceAfter=20,
            spaceBefore=20,
            textColor='black'
        )

        heading2_style = ParagraphStyle(
            'CustomHeading2',
            parent=styles['Heading2'],
            fontName=font_name_bold,
            fontSize=14,
            spaceAfter=15,
            spaceBefore=15,
            textColor='black'
        )

        heading3_style = ParagraphStyle(
            'CustomHeading3',
            parent=styles['Heading3'],
            fontName=font_name_bold,
            fontSize=12,
            spaceAfter=12,
            spaceBefore=12,
            textColor='black'
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=font_name,
            fontSize=12,
            spaceAfter=12,
            leftIndent=0,
            rightIndent=0,
            textColor='black',
            leading=16,  # 行间距
            wordWrap='LTR'  # 文字换行方向
        )

        # 构建文档内容
        story = []

        # 根据语言选择标题
        if language == "zh":
            title_text = "FindU 项目需求文档"
        else:
            title_text = "FindU Project Requirements Document"

        story.append(Paragraph(title_text, title_style))
        story.append(Spacer(1, 20))

        # 处理内容，按行分割并转换为段落
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line:
                # 对内容进行编码处理，确保特殊字符正确显示
                try:
                    # 首先尝试UTF-8编码
                    if isinstance(line, str):
                        processed_line = line
                    else:
                        processed_line = line.decode('utf-8')
                except:
                    # 如果编码失败，使用ASCII字符替换
                    processed_line = line.encode('ascii', 'ignore').decode('ascii')
                
                # HTML转义
                processed_line = (processed_line
                                .replace('&', '&amp;')
                                .replace('<', '&lt;')
                                .replace('>', '&gt;')
                                .replace('"', '&quot;')
                                .replace("'", '&#x27;'))
                
                # 简单的 Markdown 标题处理
                if processed_line.startswith('# '):
                    story.append(Paragraph(processed_line[2:], heading1_style))
                elif processed_line.startswith('## '):
                    story.append(Paragraph(processed_line[3:], heading2_style))
                elif processed_line.startswith('### '):
                    story.append(Paragraph(processed_line[4:], heading3_style))
                elif processed_line.startswith('- ') or processed_line.startswith('* '):
                    # 处理列表项
                    list_item = f"• {processed_line[2:]}"
                    list_style = ParagraphStyle(
                        'ListItem',
                        parent=normal_style,
                        leftIndent=20,
                        bulletIndent=10,
                        fontName=font_name
                    )
                    story.append(Paragraph(list_item, list_style))
                elif processed_line.startswith('**') and processed_line.endswith('**'):
                    # 处理粗体文本
                    bold_text = processed_line[2:-2]
                    bold_style = ParagraphStyle(
                        'BoldText',
                        parent=normal_style,
                        fontName=font_name_bold
                    )
                    story.append(Paragraph(bold_text, bold_style))
                else:
                    # 处理普通段落
                    story.append(Paragraph(processed_line, normal_style))
            else:
                story.append(Spacer(1, 8))

        # 生成 PDF，增加错误处理
        try:
            doc.build(story)
            logger.info(f"成功使用 reportlab 生成 PDF: {output_file}")
            return True
        except Exception as build_error:
            logger.error(f"PDF构建失败: {build_error}")
            # 尝试使用更简单的文档结构重新生成
            return _generate_simple_pdf(content, output_file)

    except Exception as e:
        logger.error(f"reportlab PDF 生成失败: {e}")
        return False

def _generate_simple_pdf(content: str, output_file: str) -> bool:
    """
    生成简化版本的PDF，避免复杂的字体和样式问题
    """
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # 创建简单的PDF画布
        c = canvas.Canvas(output_file, pagesize=A4)
        width, height = A4
        
        # 设置基本字体
        c.setFont("Helvetica-Bold", 16)
        
        # 添加标题
        title = "FindU Project Requirements Document"
        c.drawCentredText(width/2, height-50, title)
        
        # 设置正文字体
        c.setFont("Helvetica", 12)
        
        # 处理内容
        y_position = height - 100
        line_height = 20
        
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and y_position > 50:  # 确保不超出页面底部
                # 简单处理，移除特殊字符
                clean_line = ''.join(char if ord(char) < 128 else '?' for char in line)
                
                # 处理长行，自动换行
                if len(clean_line) > 80:
                    words = clean_line.split(' ')
                    current_line = ""
                    for word in words:
                        if len(current_line + word) < 80:
                            current_line += word + " "
                        else:
                            if current_line:
                                c.drawString(50, y_position, current_line.strip())
                                y_position -= line_height
                            current_line = word + " "
                    if current_line:
                        c.drawString(50, y_position, current_line.strip())
                        y_position -= line_height
                else:
                    c.drawString(50, y_position, clean_line)
                    y_position -= line_height
        
        c.save()
        logger.info(f"成功生成简化版PDF: {output_file}")
        return True
        
    except Exception as e:
        logger.error(f"简化版PDF生成也失败: {e}")
        return False

def _generate_text_file(content: str, locale: str, output_dir: str) -> str:
    """
    生成文本文件作为 PDF 的降级方案

    Args:
        content (str): 文档内容
        locale (str): 语言代码
        output_dir (str): 输出目录

    Returns:
        str: 生成的文件路径
    """
    import time
    timestamp = int(time.time())
    output_file = f"{output_dir}/demand_{locale}_{timestamp}.txt"

    # 添加文件头说明
    header = f"""
=== FindU 项目需求文档 ===
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
语言: {'中文' if locale == 'zh' else 'English'}
格式: 文本格式 (PDF 功能暂不可用)

{'=' * 50}

"""

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(header + content)

    logger.info(f"生成文本文件: {output_file}")
    return output_file

def generate_pdf_document(content: str, locale: str) -> str:
    """
    生成文档文件（PDF 或文本格式）

    将文本内容转换为文档文件并返回可访问的 URL。
    优先尝试生成 PDF，如果系统不支持则降级为文本文件。

    处理流程：
    1. 检查系统是否支持 PDF 生成
    2. 从环境变量获取存储路径配置
    3. 确保输出目录存在
    4. 生成唯一的文件名
    5. 尝试生成 PDF，失败则生成文本文件
    6. 返回可访问的 URL

    Args:
        content (str): 要转换的文档内容，支持 HTML 和 Markdown 格式
        locale (str): 语言代码，用于文件命名

    Returns:
        str: 生成的文档文件的完整访问 URL

    Raises:
        OSError: 当无法创建输出目录时
        IOError: 当文档生成失败时

    Example:
        >>> content = "# 项目需求文档\n\n这是一个示例文档。"
        >>> url = generate_pdf_document(content, "zh")
        >>> print(url)
        "http://localhost:8000/static/demands/demand_zh_1234567890.pdf"

    Note:
        - 如果系统未安装中文字体，会自动尝试下载开源字体
        - 生产环境中建议预安装中文字体以提高性能
        - 文件名使用时间戳确保唯一性
    """
    try:
        # 从环境变量获取存储路径，默认为 static/demands
        output_dir = os.getenv("STORAGE_PATH", "static/demands")

        # 确保输出目录存在，如果不存在则递归创建
        os.makedirs(output_dir, exist_ok=True)

        # 检查是否可以生成 PDF
        can_generate_pdf = REPORTLAB_AVAILABLE

        if can_generate_pdf:
            try:
                # 生成 PDF 文件
                import time
                timestamp = int(time.time())
                output_file = f"{output_dir}/demand_{locale}_{timestamp}.pdf"

                # 使用 reportlab 生成 PDF，传递语言参数
                if _generate_pdf_with_reportlab(content, output_file, locale):
                    logger.info(f"成功生成 PDF 文件: {output_file}")
                else:
                    logger.warning("reportlab PDF 生成失败, 降级为文本文件")
                    output_file = _generate_text_file(content, locale, output_dir)

            except Exception as pdf_error:
                logger.warning(f"PDF 生成失败: {str(pdf_error)}, 降级为文本文件")
                output_file = _generate_text_file(content, locale, output_dir)
        else:
            logger.info("PDF 功能不可用，生成文本文件")
            output_file = _generate_text_file(content, locale, output_dir)

        # 构造完整的访问 URL
        base_url = os.getenv('STORAGE_URL', 'http://localhost:8000')
        return f"{base_url}/{output_file}"

    except Exception as e:
        logger.error(f"文档生成失败: {str(e)}")
        raise IOError(f"Failed to generate document: {str(e)}")