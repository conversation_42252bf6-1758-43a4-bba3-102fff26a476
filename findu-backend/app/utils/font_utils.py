"""
字体管理工具函数

该模块提供字体管理相关的工具函数，包括：
- 字体文件验证
- 字体信息提取
- 字体测试和诊断
- 字体配置验证

这些工具函数为字体管理系统提供辅助功能。
"""

import os
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

def validate_font_file(font_path: str) -> bool:
    """
    验证字体文件是否有效
    
    Args:
        font_path: 字体文件路径
        
    Returns:
        bool: 字体文件是否有效
    """
    try:
        if not os.path.exists(font_path):
            return False
        
        # 检查文件大小
        file_size = os.path.getsize(font_path)
        if file_size < 1024:  # 小于1KB的文件可能不是有效字体
            return False
        
        # 检查文件扩展名
        valid_extensions = {'.ttf', '.otf', '.ttc', '.woff', '.woff2'}
        file_ext = Path(font_path).suffix.lower()
        if file_ext not in valid_extensions:
            return False
        
        # 尝试读取文件头部
        with open(font_path, 'rb') as f:
            header = f.read(4)
            
            # TTF/OTF 文件头部检查
            if file_ext in {'.ttf', '.otf'}:
                # TTF: 0x00010000 或 'OTTO'
                # OTF: 'OTTO'
                if header == b'\x00\x01\x00\x00' or header == b'OTTO':
                    return True
            elif file_ext == '.ttc':
                # TTC: 'ttcf'
                if header == b'ttcf':
                    return True
            elif file_ext in {'.woff', '.woff2'}:
                # WOFF: 'wOFF'
                # WOFF2: 'wOF2'
                if header == b'wOFF' or header == b'wOF2':
                    return True
        
        return False
        
    except Exception as e:
        logger.error(f"验证字体文件失败 {font_path}: {e}")
        return False

def get_font_info(font_path: str) -> Optional[Dict[str, str]]:
    """
    获取字体文件的基本信息
    
    Args:
        font_path: 字体文件路径
        
    Returns:
        Dict[str, str]: 字体信息字典，包含名称、格式、大小等
    """
    try:
        if not validate_font_file(font_path):
            return None
        
        file_path = Path(font_path)
        file_size = os.path.getsize(font_path)
        
        info = {
            'name': file_path.stem,
            'path': str(file_path.absolute()),
            'format': file_path.suffix.lower().lstrip('.'),
            'size': file_size,
            'size_mb': round(file_size / (1024 * 1024), 2)
        }
        
        # 尝试从文件名推断字体属性
        name_lower = file_path.stem.lower()
        
        # 推断字体粗细
        if 'bold' in name_lower:
            info['weight'] = 'bold'
        elif 'light' in name_lower:
            info['weight'] = 'light'
        elif 'thin' in name_lower:
            info['weight'] = 'thin'
        elif 'medium' in name_lower:
            info['weight'] = 'medium'
        else:
            info['weight'] = 'regular'
        
        # 推断字体样式
        if 'italic' in name_lower or 'oblique' in name_lower:
            info['style'] = 'italic'
        else:
            info['style'] = 'normal'
        
        # 推断语言支持
        if any(keyword in name_lower for keyword in ['sc', 'chinese', 'zh', 'cjk', 'noto']):
            info['language'] = 'zh'
        else:
            info['language'] = 'en'
        
        return info
        
    except Exception as e:
        logger.error(f"获取字体信息失败 {font_path}: {e}")
        return None

def discover_fonts_in_directory(directory: str) -> List[Dict[str, str]]:
    """
    发现目录中的所有字体文件
    
    Args:
        directory: 要搜索的目录路径
        
    Returns:
        List[Dict[str, str]]: 发现的字体信息列表
    """
    fonts = []
    
    try:
        if not os.path.exists(directory):
            logger.warning(f"字体目录不存在: {directory}")
            return fonts
        
        font_extensions = {'.ttf', '.otf', '.ttc', '.woff', '.woff2'}
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if any(file.lower().endswith(ext) for ext in font_extensions):
                    font_path = os.path.join(root, file)
                    font_info = get_font_info(font_path)
                    if font_info:
                        fonts.append(font_info)
        
        logger.info(f"在 {directory} 中发现 {len(fonts)} 个字体文件")
        
    except Exception as e:
        logger.error(f"搜索字体文件失败 {directory}: {e}")
    
    return fonts

def test_font_rendering(font_path: str, test_text: str = "测试文本 Test Text") -> bool:
    """
    测试字体是否能正确渲染指定文本
    
    Args:
        font_path: 字体文件路径
        test_text: 测试文本
        
    Returns:
        bool: 是否能正确渲染
    """
    try:
        # 尝试使用 ReportLab 测试字体渲染
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import tempfile
        
        # 注册测试字体
        test_font_name = f"TestFont_{os.path.basename(font_path)}"
        
        if font_path.lower().endswith('.ttc'):
            pdfmetrics.registerFont(TTFont(test_font_name, font_path, subfontIndex=0))
        else:
            pdfmetrics.registerFont(TTFont(test_font_name, font_path))
        
        # 创建临时PDF测试渲染
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=True) as temp_file:
            c = canvas.Canvas(temp_file.name, pagesize=letter)
            c.setFont(test_font_name, 12)
            c.drawString(100, 750, test_text)
            c.save()
            
            # 检查文件是否成功创建且有内容
            return os.path.getsize(temp_file.name) > 0
        
    except Exception as e:
        logger.error(f"测试字体渲染失败 {font_path}: {e}")
        return False

def validate_font_config(config_path: str) -> Tuple[bool, List[str]]:
    """
    验证字体配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Tuple[bool, List[str]]: (是否有效, 错误信息列表)
    """
    errors = []
    
    try:
        if not os.path.exists(config_path):
            errors.append(f"配置文件不存在: {config_path}")
            return False, errors
        
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必需的顶级字段
        required_fields = ['version', 'languages']
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需字段: {field}")
        
        # 检查语言配置
        languages = config.get('languages', {})
        if not languages:
            errors.append("未配置任何语言")
        
        for lang_code, lang_config in languages.items():
            # 检查语言配置必需字段
            lang_required = ['display_name', 'primary_fonts']
            for field in lang_required:
                if field not in lang_config:
                    errors.append(f"语言 {lang_code} 缺少必需字段: {field}")
            
            # 检查字体文件是否存在
            primary_fonts = lang_config.get('primary_fonts', [])
            for font_config in primary_fonts:
                font_path = font_config.get('file_path', '')
                if font_path:
                    # 如果是相对路径，相对于配置文件目录
                    if not os.path.isabs(font_path):
                        config_dir = os.path.dirname(config_path)
                        font_path = os.path.join(config_dir, font_path)
                    
                    if not validate_font_file(font_path):
                        errors.append(f"语言 {lang_code} 的字体文件无效: {font_path}")
        
        return len(errors) == 0, errors
        
    except json.JSONDecodeError as e:
        errors.append(f"配置文件JSON格式错误: {e}")
        return False, errors
    except Exception as e:
        errors.append(f"验证配置文件失败: {e}")
        return False, errors

def generate_font_report(fonts_dir: str) -> Dict[str, any]:
    """
    生成字体目录的详细报告
    
    Args:
        fonts_dir: 字体目录路径
        
    Returns:
        Dict[str, any]: 字体报告
    """
    report = {
        'directory': fonts_dir,
        'total_fonts': 0,
        'by_language': {},
        'by_format': {},
        'by_weight': {},
        'valid_fonts': 0,
        'invalid_fonts': 0,
        'fonts': []
    }
    
    try:
        fonts = discover_fonts_in_directory(fonts_dir)
        report['total_fonts'] = len(fonts)
        report['fonts'] = fonts
        
        for font in fonts:
            # 按语言统计
            lang = font.get('language', 'unknown')
            report['by_language'][lang] = report['by_language'].get(lang, 0) + 1
            
            # 按格式统计
            format_type = font.get('format', 'unknown')
            report['by_format'][format_type] = report['by_format'].get(format_type, 0) + 1
            
            # 按粗细统计
            weight = font.get('weight', 'unknown')
            report['by_weight'][weight] = report['by_weight'].get(weight, 0) + 1
            
            # 验证字体
            if validate_font_file(font['path']):
                report['valid_fonts'] += 1
            else:
                report['invalid_fonts'] += 1
        
        logger.info(f"生成字体报告完成: {report['total_fonts']} 个字体")
        
    except Exception as e:
        logger.error(f"生成字体报告失败: {e}")
        report['error'] = str(e)
    
    return report
