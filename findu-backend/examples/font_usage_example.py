#!/usr/bin/env python3
"""
字体管理系统使用示例

该示例展示了如何使用新的字体管理系统：
1. 基本字体管理操作
2. PDF文档生成
3. Word文档生成
4. 字体测试和验证

运行方式:
    cd findu-backend
    python examples/font_usage_example.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def example_basic_font_management():
    """基本字体管理示例"""
    print("=== 基本字体管理示例 ===")
    
    from app.core.font_manager import get_font_manager
    
    # 获取字体管理器实例
    font_manager = get_font_manager()
    
    # 列出支持的语言
    languages = font_manager.get_supported_languages()
    print(f"支持的语言: {languages}")
    
    # 检查语言支持
    for lang in ['zh', 'en', 'fr']:
        supported = font_manager.is_language_supported(lang)
        display_name = font_manager.get_language_display_name(lang)
        print(f"{display_name} ({lang}): {'✅ 支持' if supported else '❌ 不支持'}")
    
    # 获取中文字体
    print("\n中文字体信息:")
    zh_font = font_manager.get_font_for_language("zh")
    if zh_font:
        print(f"  字体名称: {zh_font.name}")
        print(f"  字体路径: {zh_font.file_path}")
        print(f"  字体粗细: {zh_font.weight.value}")
        print(f"  字体样式: {zh_font.style.value}")
        print(f"  字体语言: {zh_font.language}")
    
    # 获取英文字体
    print("\n英文字体信息:")
    en_font = font_manager.get_font_for_language("en")
    if en_font:
        print(f"  字体名称: {en_font.name}")
        print(f"  字体路径: {en_font.file_path}")
        print(f"  字体粗细: {en_font.weight.value}")
        print(f"  字体样式: {en_font.style.value}")
        print(f"  字体语言: {en_font.language}")
    
    print()

def example_font_registration():
    """字体注册示例"""
    print("=== 字体注册示例 ===")
    
    from app.core.font_manager import get_font_manager
    
    font_manager = get_font_manager()
    
    # 注册中文字体到 ReportLab
    print("注册中文字体到 ReportLab...")
    zh_results = font_manager.register_fonts_to_reportlab("zh")
    if zh_results:
        successful = sum(1 for success in zh_results.values() if success)
        total = len(zh_results)
        print(f"  中文字体注册结果: {successful}/{total} 成功")
        
        for font_name, success in zh_results.items():
            status = "✅" if success else "❌"
            print(f"    {status} {font_name}")
    
    # 注册英文字体到 ReportLab
    print("\n注册英文字体到 ReportLab...")
    en_results = font_manager.register_fonts_to_reportlab("en")
    if en_results:
        successful = sum(1 for success in en_results.values() if success)
        total = len(en_results)
        print(f"  英文字体注册结果: {successful}/{total} 成功")
        
        for font_name, success in en_results.items():
            status = "✅" if success else "❌"
            print(f"    {status} {font_name}")
    
    print()

def example_pdf_generation():
    """PDF生成示例"""
    print("=== PDF文档生成示例 ===")
    
    from app.services.document_service import DocumentService, DocumentFormat
    
    # 创建文档服务实例
    doc_service = DocumentService()
    
    # 中文PDF内容
    zh_content = """# FindU 项目需求文档

## 项目概述
这是一个使用新字体管理系统生成的中文PDF文档示例。

## 主要功能
- 支持多语言字体配置
- 自动字体发现和注册
- 灵活的回退机制
- 项目内置字体，减少系统依赖

## 技术特性
- **字体管理**: 统一的字体管理接口
- **多语言支持**: 支持中文、英文等多种语言
- **文档生成**: 支持PDF、Word、TXT格式

这是一个测试段落，用于验证中文字体的渲染效果。包含了常用的中文字符和标点符号。
"""
    
    # 英文PDF内容
    en_content = """# FindU Project Requirements Document

## Project Overview
This is an English PDF document example generated using the new font management system.

## Key Features
- Multi-language font configuration support
- Automatic font discovery and registration
- Flexible fallback mechanisms
- Project-embedded fonts to reduce system dependencies

## Technical Features
- **Font Management**: Unified font management interface
- **Multi-language Support**: Support for Chinese, English, and other languages
- **Document Generation**: Support for PDF, Word, TXT formats

This is a test paragraph to verify the rendering effect of English fonts. It contains commonly used English characters and punctuation marks.
"""
    
    try:
        # 生成中文PDF
        print("生成中文PDF文档...")
        zh_pdf_data, zh_mime_type = doc_service.generate_document(
            zh_content, DocumentFormat.PDF, "FindU需求文档", "zh"
        )
        print(f"  中文PDF生成成功，大小: {len(zh_pdf_data)} 字节")
        
        # 保存中文PDF到文件
        zh_output_file = "example_zh_document.pdf"
        with open(zh_output_file, 'wb') as f:
            f.write(zh_pdf_data)
        print(f"  中文PDF已保存到: {zh_output_file}")
        
        # 生成英文PDF
        print("\n生成英文PDF文档...")
        en_pdf_data, en_mime_type = doc_service.generate_document(
            en_content, DocumentFormat.PDF, "FindU Requirements Document", "en"
        )
        print(f"  英文PDF生成成功，大小: {len(en_pdf_data)} 字节")
        
        # 保存英文PDF到文件
        en_output_file = "example_en_document.pdf"
        with open(en_output_file, 'wb') as f:
            f.write(en_pdf_data)
        print(f"  英文PDF已保存到: {en_output_file}")
        
    except Exception as e:
        print(f"  ❌ PDF生成失败: {e}")
    
    print()

def example_word_generation():
    """Word文档生成示例"""
    print("=== Word文档生成示例 ===")
    
    from app.services.document_service import DocumentService, DocumentFormat
    
    # 创建文档服务实例
    doc_service = DocumentService()
    
    # 简单的测试内容
    zh_content = """# 中文Word文档测试

这是一个中文Word文档生成测试。

## 功能特性
- 支持中文字体
- 自动字体选择
- 多语言支持

测试段落：这里包含了中文字符，用于验证字体渲染效果。
"""
    
    en_content = """# English Word Document Test

This is an English Word document generation test.

## Features
- English font support
- Automatic font selection
- Multi-language support

Test paragraph: This contains English characters to verify font rendering effects.
"""
    
    try:
        # 生成中文Word文档
        print("生成中文Word文档...")
        zh_docx_data, zh_mime_type = doc_service.generate_document(
            zh_content, DocumentFormat.DOCX, "中文测试文档", "zh"
        )
        print(f"  中文Word生成成功，大小: {len(zh_docx_data)} 字节")
        
        # 保存中文Word到文件
        zh_output_file = "example_zh_document.docx"
        with open(zh_output_file, 'wb') as f:
            f.write(zh_docx_data)
        print(f"  中文Word已保存到: {zh_output_file}")
        
        # 生成英文Word文档
        print("\n生成英文Word文档...")
        en_docx_data, en_mime_type = doc_service.generate_document(
            en_content, DocumentFormat.DOCX, "English Test Document", "en"
        )
        print(f"  英文Word生成成功，大小: {len(en_docx_data)} 字节")
        
        # 保存英文Word到文件
        en_output_file = "example_en_document.docx"
        with open(en_output_file, 'wb') as f:
            f.write(en_docx_data)
        print(f"  英文Word已保存到: {en_output_file}")
        
    except Exception as e:
        print(f"  ❌ Word生成失败: {e}")
    
    print()

def example_font_validation():
    """字体验证示例"""
    print("=== 字体验证示例 ===")
    
    from app.utils.font_utils import validate_font_config, generate_font_report
    from app.core.font_manager import get_font_manager
    
    font_manager = get_font_manager()
    
    # 验证字体配置
    print("验证字体配置文件...")
    is_valid, errors = validate_font_config(font_manager.config_file)
    
    if is_valid:
        print("  ✅ 字体配置文件验证通过")
    else:
        print("  ❌ 字体配置文件验证失败")
        for error in errors:
            print(f"    • {error}")
    
    # 生成字体报告
    print("\n生成字体报告...")
    report = generate_font_report(font_manager.fonts_dir)
    
    print(f"  字体目录: {report['directory']}")
    print(f"  总字体数: {report['total_fonts']}")
    print(f"  有效字体: {report['valid_fonts']}")
    print(f"  无效字体: {report['invalid_fonts']}")
    
    if report['by_language']:
        print("  按语言分布:")
        for lang, count in report['by_language'].items():
            print(f"    {lang}: {count} 个字体")
    
    print()

def main():
    """主函数"""
    print("FindU 字体管理系统使用示例")
    print("=" * 50)
    
    try:
        # 运行各个示例
        example_basic_font_management()
        example_font_registration()
        example_pdf_generation()
        example_word_generation()
        example_font_validation()
        
        print("🎉 所有示例运行完成！")
        print("\n生成的文件:")
        for filename in ["example_zh_document.pdf", "example_en_document.pdf", 
                        "example_zh_document.docx", "example_en_document.docx"]:
            if os.path.exists(filename):
                print(f"  • {filename}")
        
    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
