"""
字体管理系统测试

该模块包含字体管理系统的单元测试，包括：
- FontManager 类的功能测试
- 字体配置加载测试
- 字体注册和获取测试
- 多语言支持测试
"""

import os
import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, MagicMock

# 导入要测试的模块
from app.core.font_manager import (
    FontManager, FontInfo, FontWeight, FontStyle, 
    LanguageFontConfig, get_font_manager, initialize_font_manager
)
from app.utils.font_utils import (
    validate_font_file, get_font_info, discover_fonts_in_directory,
    validate_font_config, generate_font_report
)

class TestFontManager:
    """FontManager 类测试"""
    
    def setup_method(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.fonts_dir = os.path.join(self.temp_dir, "fonts")
        os.makedirs(self.fonts_dir, exist_ok=True)
        
        # 创建测试字体配置
        self.test_config = {
            "version": "1.0",
            "languages": {
                "zh": {
                    "display_name": "中文",
                    "primary_fonts": [
                        {
                            "name": "TestFont-Regular",
                            "file_path": "test_font.ttf",
                            "weight": "regular",
                            "style": "normal",
                            "language": "zh"
                        }
                    ],
                    "fallback_fonts": [],
                    "default_weight": "regular",
                    "default_style": "normal"
                }
            }
        }
        
        self.config_file = os.path.join(self.fonts_dir, "font_config.json")
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_config, f, indent=2, ensure_ascii=False)
    
    def teardown_method(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_font_manager_initialization(self):
        """测试 FontManager 初始化"""
        font_manager = FontManager(self.fonts_dir, self.config_file)
        
        assert font_manager.fonts_dir == self.fonts_dir
        assert font_manager.config_file == self.config_file
        assert isinstance(font_manager._language_configs, dict)
        assert isinstance(font_manager._registered_fonts, dict)
    
    def test_load_config(self):
        """测试配置加载"""
        font_manager = FontManager(self.fonts_dir, self.config_file)
        
        # 检查是否正确加载了中文配置
        assert "zh" in font_manager._language_configs
        zh_config = font_manager._language_configs["zh"]
        assert zh_config.display_name == "中文"
        assert zh_config.language == "zh"
        assert len(zh_config.primary_fonts) == 1
    
    def test_get_supported_languages(self):
        """测试获取支持的语言列表"""
        font_manager = FontManager(self.fonts_dir, self.config_file)
        languages = font_manager.get_supported_languages()
        
        assert "zh" in languages
        assert isinstance(languages, list)
    
    def test_is_language_supported(self):
        """测试语言支持检查"""
        font_manager = FontManager(self.fonts_dir, self.config_file)
        
        assert font_manager.is_language_supported("zh") == True
        assert font_manager.is_language_supported("fr") == False
    
    def test_get_language_display_name(self):
        """测试获取语言显示名称"""
        font_manager = FontManager(self.fonts_dir, self.config_file)
        
        assert font_manager.get_language_display_name("zh") == "中文"
        assert font_manager.get_language_display_name("unknown") == "unknown"
    
    @patch('app.core.font_manager.pdfmetrics')
    def test_register_fonts_to_reportlab(self, mock_pdfmetrics):
        """测试字体注册到 ReportLab"""
        # 模拟 ReportLab 的行为
        mock_pdfmetrics.getRegisteredFontNames.return_value = []
        mock_pdfmetrics.registerFont = MagicMock()
        
        # 创建一个模拟的字体文件
        test_font_path = os.path.join(self.fonts_dir, "test_font.ttf")
        with open(test_font_path, 'wb') as f:
            f.write(b'\x00\x01\x00\x00' + b'0' * 1000)  # 模拟TTF文件头
        
        # 更新配置中的字体路径
        self.test_config["languages"]["zh"]["primary_fonts"][0]["file_path"] = "test_font.ttf"
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_config, f, indent=2, ensure_ascii=False)
        
        font_manager = FontManager(self.fonts_dir, self.config_file)
        results = font_manager.register_fonts_to_reportlab("zh")
        
        assert isinstance(results, dict)
        # 验证是否尝试注册字体
        assert mock_pdfmetrics.registerFont.called
    
    def test_get_font_for_language(self):
        """测试获取指定语言的字体"""
        font_manager = FontManager(self.fonts_dir, self.config_file)
        
        # 测试获取中文字体
        font = font_manager.get_font_for_language("zh")
        if font:  # 如果配置正确且字体文件存在
            assert font.language == "zh"
            assert isinstance(font, FontInfo)
    
    def test_reload_config(self):
        """测试重新加载配置"""
        font_manager = FontManager(self.fonts_dir, self.config_file)
        
        # 修改配置文件
        new_config = self.test_config.copy()
        new_config["languages"]["en"] = {
            "display_name": "English",
            "primary_fonts": [],
            "fallback_fonts": [],
            "default_weight": "regular",
            "default_style": "normal"
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(new_config, f, indent=2, ensure_ascii=False)
        
        # 重新加载配置
        result = font_manager.reload_config()
        assert result == True
        assert "en" in font_manager.get_supported_languages()

class TestFontUtils:
    """字体工具函数测试"""
    
    def setup_method(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_validate_font_file_nonexistent(self):
        """测试验证不存在的字体文件"""
        result = validate_font_file("/nonexistent/font.ttf")
        assert result == False
    
    def test_validate_font_file_invalid_extension(self):
        """测试验证无效扩展名的文件"""
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("not a font")
        
        result = validate_font_file(test_file)
        assert result == False
    
    def test_validate_font_file_valid_ttf(self):
        """测试验证有效的TTF文件"""
        test_file = os.path.join(self.temp_dir, "test.ttf")
        # 创建一个模拟的TTF文件头
        with open(test_file, 'wb') as f:
            f.write(b'\x00\x01\x00\x00' + b'0' * 1000)  # TTF文件头 + 足够的内容
        
        result = validate_font_file(test_file)
        assert result == True
    
    def test_get_font_info(self):
        """测试获取字体信息"""
        test_file = os.path.join(self.temp_dir, "TestFont-Bold.ttf")
        with open(test_file, 'wb') as f:
            f.write(b'\x00\x01\x00\x00' + b'0' * 1000)
        
        info = get_font_info(test_file)
        assert info is not None
        assert info['name'] == 'TestFont-Bold'
        assert info['format'] == 'ttf'
        assert info['weight'] == 'bold'
        assert info['style'] == 'normal'
    
    def test_discover_fonts_in_directory(self):
        """测试在目录中发现字体"""
        # 创建测试字体文件
        test_files = ['font1.ttf', 'font2.otf', 'not_font.txt']
        for filename in test_files:
            filepath = os.path.join(self.temp_dir, filename)
            if filename.endswith(('.ttf', '.otf')):
                with open(filepath, 'wb') as f:
                    f.write(b'\x00\x01\x00\x00' + b'0' * 1000)
            else:
                with open(filepath, 'w') as f:
                    f.write("not a font")
        
        fonts = discover_fonts_in_directory(self.temp_dir)
        
        # 应该只发现2个有效字体文件
        assert len(fonts) == 2
        font_names = [font['name'] for font in fonts]
        assert 'font1' in font_names
        assert 'font2' in font_names
    
    def test_validate_font_config_valid(self):
        """测试验证有效的字体配置"""
        config = {
            "version": "1.0",
            "languages": {
                "zh": {
                    "display_name": "中文",
                    "primary_fonts": []
                }
            }
        }
        
        config_file = os.path.join(self.temp_dir, "config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f)
        
        is_valid, errors = validate_font_config(config_file)
        assert is_valid == True
        assert len(errors) == 0
    
    def test_validate_font_config_invalid(self):
        """测试验证无效的字体配置"""
        config = {
            "languages": {}  # 缺少 version 字段
        }
        
        config_file = os.path.join(self.temp_dir, "config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f)
        
        is_valid, errors = validate_font_config(config_file)
        assert is_valid == False
        assert len(errors) > 0
        assert any("version" in error for error in errors)
    
    def test_generate_font_report(self):
        """测试生成字体报告"""
        # 创建测试字体文件
        test_files = [
            ('zh_font.ttf', 'zh'),
            ('en_font.ttf', 'en'),
            ('bold_font.ttf', 'en')
        ]
        
        for filename, lang in test_files:
            filepath = os.path.join(self.temp_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(b'\x00\x01\x00\x00' + b'0' * 1000)
        
        report = generate_font_report(self.temp_dir)
        
        assert report['directory'] == self.temp_dir
        assert report['total_fonts'] == 3
        assert 'by_language' in report
        assert 'by_format' in report
        assert 'by_weight' in report
        assert len(report['fonts']) == 3

class TestGlobalFontManager:
    """全局字体管理器测试"""
    
    def test_get_font_manager_singleton(self):
        """测试全局字体管理器单例模式"""
        manager1 = get_font_manager()
        manager2 = get_font_manager()
        
        # 应该返回同一个实例
        assert manager1 is manager2
    
    def test_initialize_font_manager(self):
        """测试初始化全局字体管理器"""
        temp_dir = tempfile.mkdtemp()
        try:
            manager = initialize_font_manager(temp_dir)
            assert manager.fonts_dir == temp_dir
            
            # 获取全局实例应该返回新初始化的管理器
            global_manager = get_font_manager()
            assert global_manager is manager
            
        finally:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)

if __name__ == "__main__":
    pytest.main([__file__])
