"""
Tests for the cases API endpoints
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import <PERSON><PERSON>lient
from httpx import AsyncClient

from app.services.ai_service import <PERSON><PERSON>ervice<PERSON><PERSON>r, AIAPIError, AIResponseError


@pytest.mark.api
class TestCasesAPI:
    """Test cases for the /api/generate-cases endpoint"""

    def test_generate_cases_success(self, client: TestClient, sample_cases):
        """Test successful case generation"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            # Mock the AI service
            mock_service = AsyncMock()
            mock_service.generate_cases.return_value = sample_cases
            mock_get_service.return_value = mock_service

            # Make the request
            response = client.post(
                "/api/generate-cases",
                json={
                    "prompt": "I want to build an e-commerce website",
                    "locale": "en"
                }
            )

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert "cases" in data
            assert len(data["cases"]) == 2
            assert data["cases"][0]["title"] == "E-commerce Website"
            assert data["cases"][1]["title"] == "Blog Platform"

            # Verify the service was called correctly
            mock_service.generate_cases.assert_called_once_with(
                "I want to build an e-commerce website", "en"
            )

    def test_generate_cases_default_locale(self, client: TestClient, sample_cases):
        """Test case generation with default locale"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.return_value = sample_cases
            mock_get_service.return_value = mock_service

            response = client.post(
                "/api/generate-cases",
                json={"prompt": "Build a website"}
            )

            assert response.status_code == 200
            # Should use default locale "en"
            mock_service.generate_cases.assert_called_once_with("Build a website", "en")

    def test_generate_cases_empty_prompt(self, client: TestClient):
        """Test case generation with empty prompt"""
        response = client.post(
            "/generate-cases",
            json={"prompt": "", "locale": "en"}
        )

        # Should return validation error for empty prompt
        assert response.status_code == 422

    def test_generate_cases_missing_prompt(self, client: TestClient):
        """Test case generation with missing prompt field"""
        response = client.post(
            "/api/generate-cases",
            json={"locale": "en"}
        )

        assert response.status_code == 422  # Validation error

    def test_generate_cases_ai_api_error(self, client: TestClient):
        """Test handling of AI API errors"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.side_effect = AIAPIError(
                "API key invalid", "openai", 401
            )
            mock_get_service.return_value = mock_service

            response = client.post(
                "/generate-cases",
                json={"prompt": "test prompt", "locale": "en"}
            )

            assert response.status_code == 503
            data = response.json()
            assert data["detail"]["error"] == "ai_api_error"
            assert "API key invalid" in data["detail"]["message"]
            assert data["detail"]["provider"] == "openai"
            assert data["detail"]["status_code"] == 401

    def test_generate_cases_ai_response_error(self, client: TestClient):
        """Test handling of AI response format errors"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.side_effect = AIResponseError(
                "Invalid JSON format"
            )
            mock_get_service.return_value = mock_service

            response = client.post(
                "/generate-cases",
                json={"prompt": "test prompt", "locale": "en"}
            )

            assert response.status_code == 422
            data = response.json()
            assert data["detail"]["error"] == "ai_response_error"
            assert "Invalid JSON format" in data["detail"]["message"]

    def test_generate_cases_general_ai_error(self, client: TestClient):
        """Test handling of general AI service errors"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.side_effect = AIServiceError(
                "Service temporarily unavailable"
            )
            mock_get_service.return_value = mock_service

            response = client.post(
                "/generate-cases",
                json={"prompt": "test prompt", "locale": "en"}
            )

            assert response.status_code == 500
            data = response.json()
            assert data["detail"]["error"] == "ai_service_error"
            assert "Service temporarily unavailable" in data["detail"]["message"]

    def test_generate_cases_unexpected_error(self, client: TestClient):
        """Test handling of unexpected errors"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.side_effect = Exception("Unexpected error")
            mock_get_service.return_value = mock_service

            response = client.post(
                "/generate-cases",
                json={"prompt": "test prompt", "locale": "en"}
            )

            assert response.status_code == 500
            data = response.json()
            assert data["detail"]["error"] == "internal_error"
            assert "Unexpected error" in data["detail"]["message"]

    @pytest.mark.asyncio
    async def test_generate_cases_async(self, async_client: AsyncClient, sample_cases):
        """Test case generation using async client"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.return_value = sample_cases
            mock_get_service.return_value = mock_service

            response = await async_client.post(
                "/generate-cases",
                json={
                    "prompt": "I want to build a mobile app",
                    "locale": "zh"
                }
            )

            assert response.status_code == 200
            data = response.json()
            assert "cases" in data
            assert len(data["cases"]) == 2

    def test_generate_cases_chinese_locale(self, client: TestClient, sample_cases):
        """Test case generation with Chinese locale"""
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.return_value = sample_cases
            mock_get_service.return_value = mock_service

            response = client.post(
                "/api/generate-cases",
                json={
                    "prompt": "我想要建立一个电商网站",
                    "locale": "zh"
                }
            )

            assert response.status_code == 200
            mock_service.generate_cases.assert_called_once_with(
                "我想要建立一个电商网站", "zh"
            )

    def test_generate_cases_long_prompt(self, client: TestClient, sample_cases):
        """Test case generation with a very long prompt"""
        long_prompt = "I want to build " + "a very detailed " * 100 + "e-commerce website"
        
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.return_value = sample_cases
            mock_get_service.return_value = mock_service

            response = client.post(
                "/api/generate-cases",
                json={"prompt": long_prompt, "locale": "en"}
            )

            assert response.status_code == 200
            mock_service.generate_cases.assert_called_once_with(long_prompt, "en")

    def test_generate_cases_special_characters(self, client: TestClient, sample_cases):
        """Test case generation with special characters in prompt"""
        special_prompt = "I want to build an e-commerce website with émojis 🛒 and spëcial chars!"
        
        with patch('app.routers.cases.get_ai_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.generate_cases.return_value = sample_cases
            mock_get_service.return_value = mock_service

            response = client.post(
                "/api/generate-cases",
                json={"prompt": special_prompt, "locale": "en"}
            )

            assert response.status_code == 200
            mock_service.generate_cases.assert_called_once_with(special_prompt, "en")

    def test_generate_cases_invalid_json(self, client: TestClient):
        """Test case generation with invalid JSON"""
        response = client.post(
            "/api/generate-cases",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )

        assert response.status_code == 422

    def test_generate_cases_wrong_content_type(self, client: TestClient):
        """Test case generation with wrong content type"""
        response = client.post(
            "/api/generate-cases",
            data="prompt=test&locale=en",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        assert response.status_code == 422
