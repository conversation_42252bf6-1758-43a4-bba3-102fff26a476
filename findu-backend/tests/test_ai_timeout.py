#!/usr/bin/env python3
"""
AI服务超时测试脚本

测试AI服务的超时处理和重试机制
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_service import AIService, AIConfig, CaseData

async def test_ai_timeout_handling():
    """测试AI服务超时处理"""
    print("=== AI服务超时处理测试 ===")
    
    # 测试配置 - 使用较短的超时时间来模拟超时
    test_config = AIConfig(
        provider="qwen",
        api_key="your_api_key_here",  # 使用无效的API密钥
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        model="qwen-turbo",
        timeout=5.0,  # 5秒超时
        max_retries=2,  # 2次重试
        temperature=0.7,
        max_tokens=1000
    )
    
    ai_service = AIService(test_config)
    
    print(f"配置信息:")
    print(f"  Provider: {test_config.provider}")
    print(f"  Model: {test_config.model}")
    print(f"  Timeout: {test_config.timeout}秒")
    print(f"  Max Retries: {test_config.max_retries}")
    print(f"  API Key: {'有效' if test_config.api_key and test_config.api_key != 'your_api_key_here' else '无效'}")
    
    # 测试1: 案例生成（预期会超时或失败）
    print("\n1. 测试案例生成:")
    try:
        start_time = asyncio.get_event_loop().time()
        cases = await ai_service.generate_cases("电子商务网站", "zh")
        end_time = asyncio.get_event_loop().time()
        
        print(f"  ✅ 案例生成成功 (耗时: {end_time - start_time:.2f}秒)")
        print(f"  生成了 {len(cases)} 个案例")
        for i, case in enumerate(cases):
            print(f"    案例{i+1}: {case.title}")
            
    except Exception as e:
        end_time = asyncio.get_event_loop().time()
        print(f"  ❌ 案例生成失败 (耗时: {end_time - start_time:.2f}秒)")
        print(f"  错误类型: {type(e).__name__}")
        print(f"  错误信息: {str(e)}")
    
    # 测试2: 文档生成（预期会超时或失败）
    print("\n2. 测试文档生成:")
    test_case = CaseData(
        id=1,
        title="电子商务网站",
        description="功能完整的在线购物平台",
        details=["用户注册登录", "商品展示", "购物车", "订单管理", "支付系统"]
    )
    
    try:
        start_time = asyncio.get_event_loop().time()
        document = await ai_service.generate_document(test_case, "zh")
        end_time = asyncio.get_event_loop().time()
        
        print(f"  ✅ 文档生成成功 (耗时: {end_time - start_time:.2f}秒)")
        print(f"  文档长度: {len(document)} 字符")
        print(f"  文档预览: {document[:100]}...")
        
    except Exception as e:
        end_time = asyncio.get_event_loop().time()
        print(f"  ❌ 文档生成失败 (耗时: {end_time - start_time:.2f}秒)")
        print(f"  错误类型: {type(e).__name__}")
        print(f"  错误信息: {str(e)}")
    
    # 清理资源
    await ai_service.close()
    
    print("\n=== 测试完成 ===")

async def test_ai_connection():
    """测试AI服务连接"""
    print("\n=== AI服务连接测试 ===")
    
    from app.services.ai_service import test_ai_service_connection
    
    # 测试当前配置
    print("测试当前配置:")
    result = await test_ai_service_connection()
    
    if result["success"]:
        print(f"  ✅ 连接成功")
        print(f"  Provider: {result['provider']}")
        print(f"  Model: {result['model']}")
        print(f"  响应: {result['response']}")
    else:
        print(f"  ❌ 连接失败")
        print(f"  Provider: {result['provider']}")
        print(f"  Model: {result['model']}")
        print(f"  错误: {result['error']}")

if __name__ == "__main__":
    asyncio.run(test_ai_timeout_handling())
    asyncio.run(test_ai_connection())
