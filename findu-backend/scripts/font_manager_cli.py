#!/usr/bin/env python3
"""
字体管理命令行工具

该脚本提供字体管理系统的命令行接口，包括：
- 列出可用字体
- 验证字体配置
- 生成字体报告
- 测试字体渲染
- 重新加载字体配置

使用方法:
    python font_manager_cli.py list                    # 列出所有字体
    python font_manager_cli.py validate               # 验证字体配置
    python font_manager_cli.py report                 # 生成字体报告
    python font_manager_cli.py test <language>        # 测试指定语言的字体
    python font_manager_cli.py reload                 # 重新加载配置
"""

import sys
import os
import argparse
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.font_manager import get_font_manager, initialize_font_manager
from app.utils.font_utils import (
    validate_font_config, generate_font_report, 
    discover_fonts_in_directory, test_font_rendering
)

def list_fonts(args):
    """列出所有可用字体"""
    font_manager = get_font_manager()
    
    print("=== FindU 字体管理系统 - 字体列表 ===\n")
    
    # 列出支持的语言
    languages = font_manager.get_supported_languages()
    print(f"支持的语言: {', '.join(languages)}\n")
    
    # 为每种语言列出字体
    for language in languages:
        display_name = font_manager.get_language_display_name(language)
        print(f"语言: {display_name} ({language})")
        print("-" * 40)
        
        fonts = font_manager.list_fonts_for_language(language)
        if fonts:
            for font in fonts:
                print(f"  • {font.name}")
                print(f"    路径: {font.file_path}")
                print(f"    粗细: {font.weight.value}, 样式: {font.style.value}")
                if font.metadata:
                    family = font.metadata.get('family', 'Unknown')
                    print(f"    字体族: {family}")
                print()
        else:
            print("  未找到字体\n")

def validate_config(args):
    """验证字体配置"""
    font_manager = get_font_manager()
    config_file = font_manager.config_file
    
    print("=== FindU 字体管理系统 - 配置验证 ===\n")
    print(f"配置文件: {config_file}")
    
    is_valid, errors = validate_font_config(config_file)
    
    if is_valid:
        print("✅ 配置文件验证通过")
        
        # 额外检查字体文件
        print("\n检查字体文件...")
        languages = font_manager.get_supported_languages()
        all_fonts_valid = True
        
        for language in languages:
            fonts = font_manager.list_fonts_for_language(language)
            for font in fonts:
                if os.path.exists(font.file_path):
                    print(f"✅ {font.name}: {font.file_path}")
                else:
                    print(f"❌ {font.name}: 文件不存在 - {font.file_path}")
                    all_fonts_valid = False
        
        if all_fonts_valid:
            print("\n🎉 所有字体文件都存在且有效")
        else:
            print("\n⚠️  部分字体文件存在问题")
            
    else:
        print("❌ 配置文件验证失败")
        print("\n错误信息:")
        for error in errors:
            print(f"  • {error}")

def generate_report(args):
    """生成字体报告"""
    font_manager = get_font_manager()
    fonts_dir = font_manager.fonts_dir
    
    print("=== FindU 字体管理系统 - 字体报告 ===\n")
    
    report = generate_font_report(fonts_dir)
    
    print(f"字体目录: {report['directory']}")
    print(f"总字体数: {report['total_fonts']}")
    print(f"有效字体: {report['valid_fonts']}")
    print(f"无效字体: {report['invalid_fonts']}")
    
    if report['by_language']:
        print("\n按语言分布:")
        for lang, count in report['by_language'].items():
            print(f"  {lang}: {count} 个字体")
    
    if report['by_format']:
        print("\n按格式分布:")
        for format_type, count in report['by_format'].items():
            print(f"  {format_type}: {count} 个字体")
    
    if report['by_weight']:
        print("\n按粗细分布:")
        for weight, count in report['by_weight'].items():
            print(f"  {weight}: {count} 个字体")
    
    if args.detailed:
        print("\n详细字体信息:")
        for font in report['fonts']:
            print(f"\n字体: {font['name']}")
            print(f"  路径: {font['path']}")
            print(f"  格式: {font['format']}")
            print(f"  大小: {font['size_mb']} MB")
            print(f"  语言: {font['language']}")
            print(f"  粗细: {font['weight']}")
            print(f"  样式: {font['style']}")
    
    # 保存报告到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n报告已保存到: {args.output}")

def test_language(args):
    """测试指定语言的字体"""
    font_manager = get_font_manager()
    language = args.language
    
    print(f"=== FindU 字体管理系统 - 测试 {language} 语言字体 ===\n")
    
    if not font_manager.is_language_supported(language):
        print(f"❌ 不支持的语言: {language}")
        print(f"支持的语言: {', '.join(font_manager.get_supported_languages())}")
        return
    
    # 获取字体
    font = font_manager.get_font_for_language(language)
    if not font:
        print(f"❌ 未找到 {language} 语言的字体")
        return
    
    print(f"测试字体: {font.name}")
    print(f"字体路径: {font.file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(font.file_path):
        print("❌ 字体文件不存在")
        return
    
    print("✅ 字体文件存在")
    
    # 测试字体注册
    try:
        results = font_manager.register_fonts_to_reportlab(language)
        if results:
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            print(f"✅ 字体注册成功: {successful}/{total}")
            
            # 获取 ReportLab 字体名称
            font_name = font_manager.get_reportlab_font_name(language)
            if font_name:
                print(f"ReportLab 字体名称: {font_name}")
            
        else:
            print("❌ 字体注册失败")
    except Exception as e:
        print(f"❌ 字体注册出错: {e}")
    
    # 测试字体渲染
    test_text = "测试文本 Test Text 123" if language == "zh" else "Test Text 123"
    print(f"\n测试渲染文本: {test_text}")
    
    try:
        if test_font_rendering(font.file_path, test_text):
            print("✅ 字体渲染测试通过")
        else:
            print("❌ 字体渲染测试失败")
    except Exception as e:
        print(f"❌ 字体渲染测试出错: {e}")

def reload_config(args):
    """重新加载字体配置"""
    font_manager = get_font_manager()
    
    print("=== FindU 字体管理系统 - 重新加载配置 ===\n")
    
    try:
        if font_manager.reload_config():
            print("✅ 配置重新加载成功")
            
            # 显示更新后的信息
            languages = font_manager.get_supported_languages()
            print(f"支持的语言: {', '.join(languages)}")
            
            total_fonts = 0
            for language in languages:
                fonts = font_manager.list_fonts_for_language(language)
                total_fonts += len(fonts)
            
            print(f"总字体数: {total_fonts}")
            
        else:
            print("❌ 配置重新加载失败")
    except Exception as e:
        print(f"❌ 重新加载配置出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="FindU 字体管理系统命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s list                    # 列出所有字体
  %(prog)s validate               # 验证字体配置
  %(prog)s report --detailed      # 生成详细字体报告
  %(prog)s report -o report.json  # 生成报告并保存到文件
  %(prog)s test zh                # 测试中文字体
  %(prog)s test en                # 测试英文字体
  %(prog)s reload                 # 重新加载配置
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # list 命令
    list_parser = subparsers.add_parser('list', help='列出所有可用字体')
    list_parser.set_defaults(func=list_fonts)
    
    # validate 命令
    validate_parser = subparsers.add_parser('validate', help='验证字体配置')
    validate_parser.set_defaults(func=validate_config)
    
    # report 命令
    report_parser = subparsers.add_parser('report', help='生成字体报告')
    report_parser.add_argument('--detailed', action='store_true', help='生成详细报告')
    report_parser.add_argument('-o', '--output', help='保存报告到文件')
    report_parser.set_defaults(func=generate_report)
    
    # test 命令
    test_parser = subparsers.add_parser('test', help='测试指定语言的字体')
    test_parser.add_argument('language', help='要测试的语言代码 (如: zh, en)')
    test_parser.set_defaults(func=test_language)
    
    # reload 命令
    reload_parser = subparsers.add_parser('reload', help='重新加载字体配置')
    reload_parser.set_defaults(func=reload_config)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        if args.command == 'test':
            print("提示: 请确保已安装 reportlab 库")

if __name__ == "__main__":
    main()
