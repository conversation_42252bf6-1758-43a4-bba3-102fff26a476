"""
FindU 后端主应用程序

该文件是 FindU AI 需求生成器的后端主入口点，基于 FastAPI 框架构建。
主要功能包括：
- 配置 FastAPI 应用实例和中间件
- 设置 CORS 跨域资源共享策略
- 初始化 JWT 认证系统
- 注册 API 路由模块
- 提供健康检查端点

技术栈：
- FastAPI: 现代高性能 Python Web 框架
- CORS: 跨域资源共享中间件
- JWT: JSON Web Token 认证
- 环境变量管理: python-dotenv

架构设计：
采用模块化路由设计，将不同功能的 API 端点分离到独立的路由模块中，
便于维护和扩展。支持前后端分离架构，通过 RESTful API 提供服务。
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv
import os
from pathlib import Path
from contextlib import asynccontextmanager
from app.routers import cases, documents
from app.routers.static import router as static_router
from app.utils.auth import init_jwt
from app.db import init_database, close_database

# 加载环境变量配置文件
# 从 .env 文件中读取配置参数，包括数据库连接、API密钥等敏感信息
load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理器

    使用现代的 FastAPI lifespan 事件处理机制，管理应用的启动和关闭过程。
    替代了已弃用的 @app.on_event 装饰器。

    启动时执行：
    - JWT 认证系统初始化
    - 数据库连接初始化（可选）

    关闭时执行：
    - 数据库连接清理
    - 其他资源释放
    """
    # 应用启动时的初始化操作
    print("🚀 FindU 后端服务启动中...")

    # 初始化 JWT 认证系统
    init_jwt(os.getenv("JWT_SECRET", "your_jwt_secret"))

    # 初始化数据库连接（可选功能）
    init_database()

    # 初始化字体管理系统
    try:
        from app.core.font_manager import get_font_manager
        font_manager = get_font_manager()
        languages = font_manager.get_supported_languages()
        print(f"📝 字体管理系统已初始化，支持语言: {', '.join(languages)}")
    except Exception as e:
        print(f"⚠️  字体管理系统初始化失败: {e}")

    print("✅ FindU 后端服务启动完成")

    # yield 分隔启动和关闭逻辑
    yield

    # 应用关闭时的清理操作
    print("🛑 FindU 后端服务关闭中...")
    close_database()
    print("✅ FindU 后端服务已安全关闭")

# 创建 FastAPI 应用实例
# 设置应用标题和生命周期管理器
app = FastAPI(
    title="AI Demand Generator API",
    lifespan=lifespan
)

# 配置 CORS 中间件，允许前端跨域请求
# 这是前后端分离架构的关键配置，确保浏览器允许前端域名访问后端 API
app.add_middleware(
    CORSMiddleware,
    # 允许的源域名，从环境变量获取前端 URL
    allow_origins=[os.getenv("FRONTEND_URL", "http://localhost:3000")],
    # 允许携带认证信息（如 cookies、authorization headers）
    allow_credentials=True,
    # 允许所有 HTTP 方法（GET, POST, PUT, DELETE 等）
    allow_methods=["*"],
    # 允许所有请求头
    allow_headers=["*"],
)

# 注册 API 路由模块
# 将案例生成相关的 API 端点注册到 /api 前缀下
app.include_router(cases.router, prefix="/api")
# 将文档生成相关的 API 端点注册到 /api 前缀下
app.include_router(documents.router, prefix="/api")
# 将静态文件管理相关的 API 端点注册到 /api 前缀下
app.include_router(static_router, prefix="/api")

# 配置静态文件服务
# 创建静态文件目录（如果不存在）
static_dir = Path("static/demands")
static_dir.mkdir(parents=True, exist_ok=True)

# 挂载静态文件目录，使文件可以通过 /static/demands/ 路径直接访问
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/health")
async def health_check():
    """
    健康检查端点

    提供简单的服务状态检查，用于：
    - 监控系统确认服务运行状态
    - 负载均衡器健康检查
    - 容器编排系统（如 Docker、Kubernetes）的就绪性探测

    Returns:
        dict: 包含服务状态的字典，格式为 {"status": "healthy"}
    """
    return {"status": "healthy"}

