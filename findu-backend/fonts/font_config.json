{"version": "1.0", "description": "FindU 字体管理配置文件", "metadata": {"created": "2025-01-21", "author": "FindU Development Team", "purpose": "统一管理多语言字体配置，支持PDF和Word文档生成"}, "languages": {"zh": {"display_name": "中文 (简体)", "description": "中文简体字体配置", "primary_fonts": [{"name": "NotoSansSC-Regular", "display_name": "思源黑体 常规", "file_path": "Noto_Sans_SC/static/NotoSansSC-Regular.ttf", "weight": "regular", "style": "normal", "language": "zh", "is_variable": false, "subfont_index": 0, "metadata": {"family": "Noto Sans SC", "designer": "Google", "license": "SIL Open Font License", "unicode_support": ["CJK", "Latin", "Punctuation"], "recommended_for": ["body_text", "headings"]}}, {"name": "NotoSansSC-Bold", "display_name": "思源黑体 粗体", "file_path": "Noto_Sans_SC/static/NotoSansSC-Bold.ttf", "weight": "bold", "style": "normal", "language": "zh", "is_variable": false, "subfont_index": 0, "metadata": {"family": "Noto Sans SC", "designer": "Google", "license": "SIL Open Font License", "unicode_support": ["CJK", "Latin", "Punctuation"], "recommended_for": ["headings", "emphasis"]}}, {"name": "NotoSansSC-Light", "display_name": "思源黑体 细体", "file_path": "Noto_Sans_SC/static/NotoSansSC-Light.ttf", "weight": "light", "style": "normal", "language": "zh", "is_variable": false, "subfont_index": 0, "metadata": {"family": "Noto Sans SC", "designer": "Google", "license": "SIL Open Font License", "unicode_support": ["CJK", "Latin", "Punctuation"], "recommended_for": ["captions", "fine_print"]}}, {"name": "NotoSansSC-Medium", "display_name": "思源黑体 中等", "file_path": "Noto_Sans_SC/static/NotoSansSC-Medium.ttf", "weight": "medium", "style": "normal", "language": "zh", "is_variable": false, "subfont_index": 0, "metadata": {"family": "Noto Sans SC", "designer": "Google", "license": "SIL Open Font License", "unicode_support": ["CJK", "Latin", "Punctuation"], "recommended_for": ["subheadings", "emphasis"]}}], "fallback_fonts": [{"name": "NotoSansSC-Variable", "display_name": "思源黑体 可变字体", "file_path": "Noto_Sans_SC/NotoSansSC-VariableFont_wght.ttf", "weight": "regular", "style": "normal", "language": "zh", "is_variable": true, "subfont_index": 0, "metadata": {"family": "Noto Sans SC", "designer": "Google", "license": "SIL Open Font License", "unicode_support": ["CJK", "Latin", "Punctuation"], "variable_axes": ["wght"], "weight_range": [100, 900], "recommended_for": ["all_purposes"]}}], "default_weight": "regular", "default_style": "normal", "font_size_recommendations": {"title": 18, "heading": 16, "subheading": 14, "body": 12, "caption": 10}}, "en": {"display_name": "English", "description": "English font configuration", "primary_fonts": [{"name": "DejaVuSans-Regular", "display_name": "DejaVu Sans Regular", "file_path": "DejaVu_Sans/DejaVuSans.ttf", "weight": "regular", "style": "normal", "language": "en", "is_variable": false, "subfont_index": 0, "metadata": {"family": "DejaVu Sans", "designer": "DejaVu Fonts Team", "license": "Bitstream Vera License", "unicode_support": ["Latin", "Latin Extended", "Cyrillic", "Greek"], "recommended_for": ["body_text", "technical_documents"]}}, {"name": "DejaVuSans-Bold", "display_name": "DejaVu Sans Bold", "file_path": "DejaVu_Sans/DejaVuSans-Bold.ttf", "weight": "bold", "style": "normal", "language": "en", "is_variable": false, "subfont_index": 0, "metadata": {"family": "DejaVu Sans", "designer": "DejaVu Fonts Team", "license": "Bitstream Vera License", "unicode_support": ["Latin", "Latin Extended", "Cyrillic", "Greek"], "recommended_for": ["headings", "emphasis"]}}], "fallback_fonts": [{"name": "Liberation-Sans", "display_name": "Liberation Sans", "file_path": "Liberation_Sans/LiberationSans-Regular.ttf", "weight": "regular", "style": "normal", "language": "en", "is_variable": false, "subfont_index": 0, "metadata": {"family": "Liberation Sans", "designer": "Red Hat", "license": "SIL Open Font License", "unicode_support": ["Latin", "Latin Extended"], "recommended_for": ["general_purpose"]}}], "default_weight": "regular", "default_style": "normal", "font_size_recommendations": {"title": 18, "heading": 16, "subheading": 14, "body": 12, "caption": 10}}}, "global_settings": {"default_language": "en", "fallback_language": "en", "enable_font_caching": true, "font_loading_timeout": 30, "auto_discover_fonts": true, "supported_formats": [".ttf", ".otf", ".ttc", ".woff", ".woff2"]}, "document_templates": {"pdf": {"title_font_size": 18, "heading_font_size": 16, "body_font_size": 12, "line_height_multiplier": 1.2, "margin_top": 72, "margin_bottom": 72, "margin_left": 72, "margin_right": 72}, "docx": {"title_font_size": 18, "heading_font_size": 16, "body_font_size": 11, "line_spacing": 1.15}}}